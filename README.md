# Deepfake Video Detector

## Project Overview

This project implements a deep learning-based system to detect deepfake videos. It utilizes a Convolutional Neural Network (CNN) to classify videos as either "Real" or "Fake". The system also incorporates Explainable AI (XAI) using Grad-CAM to visualize the regions of a video frame that the model focuses on when making its predictions, providing insights into its decision-making process.

The primary goal is to build an end-to-end pipeline, from data preprocessing and model training to an interactive Streamlit application for demonstration.

## Features

*   **Deepfake Detection:** Classifies input videos as "Real" or "Deepfake" using an EfficientNet-B0 based model.
*   **Frame-Level Analysis:** The model processes individual video frames to make predictions.
*   **Video-Level Aggregation:** Frame predictions are aggregated (e.g., by averaging probabilities) to produce a final video-level classification.
*   **Explainable AI (XAI):** Uses Grad-CAM to generate heatmaps, highlighting the image regions most influential for the model's decision on a per-frame basis.
*   **Modular Codebase:** Organized into source code for preprocessing, model definition, training, explainability, and a Streamlit application.
*   **Streamlit Application:** Provides a user-friendly web interface to upload videos, view predictions, and see XAI explanations.

## Directory Structure

```
deepfake_detector/
├── app.py                  # Main Streamlit application script
├── data/                   # Placeholder for raw video data and processed frames
│   ├── raw/                # Expected location for raw train/val/test videos
│   ├── frames/             # Output location for extracted frames (created by preprocess.py)
│   └── grad_cam_outputs/   # Example output location for Grad-CAM images from explainability.py
├── docs/                   # Documentation files
│   ├── evaluation_strategy.md    # Details on model evaluation metrics and protocols
│   └── optimization_strategies.md # Strategies for model and pipeline optimization
├── models/                 # Saved model checkpoints
│   └── deepfake_detector_best.pth # Example trained model checkpoint
├── notebooks/              # Jupyter notebooks for experimentation (if any)
├── src/                    # Source code
│   ├── __init__.py
│   ├── model.py            # Defines the DeepfakeDetector neural network model
│   ├── preprocess.py       # Script for video-to-frame extraction
│   ├── train.py            # Script for training the model
│   └── explainability.py   # Script for generating Grad-CAM explanations
├── requirements.txt        # Python package dependencies
└── README.md               # This file: project overview, setup, and usage instructions
```

## Setup and Installation

### 1. Prerequisites
*   Python 3.8+
*   `pip` for package management
*   Access to a terminal or command prompt.

### 2. Clone the Repository (if applicable)
```bash
git clone <repository_url>
cd deepfake_detector
```

### 3. Create a Virtual Environment
It's highly recommended to use a virtual environment to manage project dependencies.

**Using `venv`:**
```bash
# Create a virtual environment
python3 -m venv venv

# Activate the virtual environment
# On macOS and Linux:
source venv/bin/activate
# On Windows:
# venv\Scripts\activate
```

**Using `conda`:**
```bash
# Create a conda environment
conda create -n deepfake_env python=3.8

# Activate the conda environment
conda activate deepfake_env
```

### 4. Install Dependencies
Install all required Python packages using the `requirements.txt` file:
```bash
pip install -r requirements.txt
```
This will install PyTorch, TorchVision, OpenCV, Streamlit, and other necessary libraries. Installation might take some time, especially for PyTorch.

## Dataset

### Recommended Dataset
This project is designed to work with datasets like the **Deepfake Detection Challenge Dataset (DFDC)**. You can find information and access it here:
*   [Kaggle Deepfake Detection Challenge](https://www.kaggle.com/c/deepfake-detection-challenge)

Other similar datasets containing 'real' and 'fake' videos can also be adapted.

### Setup
1.  **Download the Dataset:** Obtain the dataset from its source. It usually comes with videos organized into training and testing sets, possibly with metadata.
2.  **Organize Raw Data:**
    *   Create a directory `deepfake_detector/data/raw/`.
    *   Within `raw/`, organize your videos. A suggested structure is:
        ```
        deepfake_detector/data/raw/
        ├── train/
        │   ├── real/ (contains real_video1.mp4, real_video2.mp4, ...)
        │   └── fake/ (contains fake_video1.mp4, fake_video2.mp4, ...)
        ├── val/      (similarly structured if you have a validation set)
        │   ├── real/
        │   └── fake/
        └── test/     (similarly structured if you have a test set)
        │   ├── real/
        │   └── fake/
        ```
    *   Update paths in `src/preprocess.py` and `src/train.py` if your structure differs.

## Usage

### 1. Preprocessing: Video to Frames
The `src/preprocess.py` script extracts frames from videos. This step is necessary before training the model.

*   **Configuration:**
    *   Open `src/preprocess.py`.
    *   Modify `VIDEO_ROOT_DIR`, `FRAME_ROOT_DIR`, and `FRAME_RATE_PER_SECOND` variables at the top of the script to match your dataset paths and desired frame extraction rate.
*   **Run the script:**
    ```bash
    python src/preprocess.py
    ```
    Extracted frames will be saved in the directory specified by `FRAME_ROOT_DIR` (e.g., `deepfake_detector/data/frames/`).

### 2. Training the Model
The `src/train.py` script trains the deepfake detection model.

*   **Configuration:**
    *   Ensure your preprocessed frames (from step 1) are available.
    *   The script expects a PyTorch `Dataset` class that can load these frames. The current `train.py` uses a placeholder `TensorDataset` for demonstration. **You will need to implement a custom `Dataset`** (e.g., in a new `dataset.py` file or within `train.py`) that reads your specific frame directory structure and applies transformations. The comments in `train.py` provide guidance.
    *   Adjust hyperparameters like `LEARNING_RATE`, `BATCH_SIZE`, `NUM_EPOCHS` in `src/train.py` as needed.
*   **Run training:**
    ```bash
    python src/train.py
    ```
*   **Model Checkpoints:** Trained model weights will be saved to `deepfake_detector/models/`. The best performing model (based on validation loss) is typically saved as `deepfake_detector_best.pth`.

### 3. Running the Streamlit Application
The `app.py` script launches an interactive web application to upload videos and see predictions.

*   **Prerequisites:**
    *   Ensure Streamlit is installed (`pip install streamlit`).
    *   A trained model checkpoint should be available at `deepfake_detector/models/deepfake_detector_best.pth`. If not, the app will use a base model or a dummy, and predictions might not be meaningful.
*   **Run the app:**
    Navigate to the `deepfake_detector` root directory in your terminal and run:
    ```bash
    streamlit run app.py
    ```
    This will open the application in your web browser.

### 4. Using the Explainability Script
The `src/explainability.py` script demonstrates how to generate Grad-CAM heatmaps for a given model and input image.

*   **Functionality:** It loads the model, prepares a sample input, and generates/saves a Grad-CAM visualization.
*   **Run the script (example):**
    ```bash
    python src/explainability.py
    ```
    Example output images will be saved in `deepfake_detector/data/grad_cam_outputs/`.
*   **Integration:** The script also contains detailed comments on how to integrate Grad-CAM generation into your evaluation pipeline (e.g., within `src/train.py`'s validation loop) to analyze model behavior on specific frames.

## Model Architecture

The deepfake detection model (`DeepfakeDetector` class in `src/model.py`) is based on a pre-trained **EfficientNet-B0** architecture.
*   **Transfer Learning:** EfficientNet-B0, pre-trained on the ImageNet dataset, is used as a feature extractor. This leverages the powerful image representations learned from a large dataset.
*   **Custom Classifier:** The original classifier head of EfficientNet-B0 is replaced with a new sequential layer consisting of a Dropout layer and a Linear layer. This new head is fine-tuned on the deepfake dataset for the binary classification task (Real vs. Fake).
*   **Input:** The model processes individual image frames (e.g., 224x224 pixels for EfficientNet-B0).

## Explainable AI (XAI)

To understand the model's decision-making process, **Grad-CAM (Gradient-weighted Class Activation Mapping)** is used.
*   **Functionality:** Grad-CAM produces a heatmap that highlights the regions in an input image that were most influential in the model's prediction for a specific class.
*   **Implementation:** The `src/explainability.py` script provides functions to generate and visualize these heatmaps using the `pytorch-grad-cam` library. The Streamlit application (`app.py`) also integrates this to show explanations for uploaded videos.

## Evaluation

The model's performance is evaluated using standard classification metrics on a held-out test set. These include:
*   Accuracy
*   Precision
*   Recall
*   F1-Score
*   AUC-ROC

A detailed evaluation strategy, including testing protocols and robustness checks, is outlined in `docs/evaluation_strategy.md`.

## Future Work / Potential Improvements

*   **Advanced Data Augmentation:** Explore more sophisticated data augmentation techniques.
*   **Temporal Modeling:** Incorporate temporal information by using models like LSTMs, GRUs, or Transformers over sequences of frame features.
*   **Face Detection and Cropping:** Integrate a face detection step to focus the model specifically on facial regions, potentially improving accuracy and reducing background noise.
*   **Larger Models/Architectures:** Experiment with larger EfficientNet variants (B1-B7) or other advanced CNN architectures.
*   **More Sophisticated XAI:** Explore other XAI techniques like SHAP.
*   **Robustness to Adversarial Attacks:** Investigate and enhance model robustness against adversarial examples specifically designed to fool deepfake detectors.

## Requirements.txt

The `requirements.txt` file lists all Python dependencies. Install them using:
```bash
pip install -r requirements.txt
```

## Acknowledgements

*   This project utilizes concepts and architectures common in the field of deep learning and computer vision.
*   Libraries like PyTorch, OpenCV, Streamlit, and pytorch-grad-cam are fundamental to this work.
*   (If applicable) Specific research papers or open-source repositories that were heavily referenced.

---

*Note: This project is for educational and demonstration purposes. The effectiveness of the deepfake detection depends significantly on the quality and size of the training dataset, as well as the model tuning.*
```
