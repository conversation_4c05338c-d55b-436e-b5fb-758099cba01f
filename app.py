import streamlit as st
import cv2
import numpy as np
import torch
from torchvision import transforms
from PIL import Image
import os
import sys
import tempfile

# --- Add src directory to Python path to import model, preprocess, explainability ---
# This assumes app.py is in deepfake_detector/ and src/ is a subdirectory.
# Adjust if your directory structure is different.
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(current_dir, "src")
if src_dir not in sys.path:
    sys.path.append(src_dir)

# --- Import project-specific modules ---
try:
    from model import DeepfakeDetector
    from explainability import generate_grad_cam_heatmap, visualize_grad_cam
    # We'll adapt frame extraction logic from preprocess.py directly in this script
    # as it's simpler for a single video upload scenario.
except ImportError as e:
    st.error(f"Error importing necessary modules from 'src': {e}. "
             f"Ensure 'src/model.py' and 'src/explainability.py' exist and are in PYTHONPATH. "
             f"The 'src' directory should be in the same directory as this 'app.py'.")
    # Provide dummy implementations if imports fail, so the app can partially run for UI checks.
    class DeepfakeDetector(torch.nn.Module):
        def __init__(self, *args, **kwargs): super().__init__(); self.dummy = True; print("Using dummy DeepfakeDetector")
        def forward(self, x): return torch.randn(x.size(0), 1)
    def generate_grad_cam_heatmap(*args, **kwargs): print("Using dummy generate_grad_cam_heatmap"); return np.random.rand(224, 224)
    def visualize_grad_cam(*args, **kwargs): print("Using dummy visualize_grad_cam"); return (np.random.rand(224, 224, 3) * 255).astype(np.uint8)

# --- Constants and Configuration ---
MODEL_PATH = os.path.join(current_dir, "models", "deepfake_detector_best.pth") # Placeholder for trained model
# MODEL_PATH = os.path.join(current_dir, "models", "deepfake_detector_final.pth") # Or final, if specified
DEFAULT_NUM_FRAMES_TO_PROCESS = 5  # Process N frames from the video for prediction
DEFAULT_NUM_FRAMES_FOR_XAI = 3    # Generate Grad-CAM for N frames

# --- Helper Functions for Video Processing ---
def extract_frames_from_video(video_file_buffer, num_frames_to_extract, frame_rate_to_sample=5):
    """
    Extracts a specified number of frames from a video file buffer.

    Args:
        video_file_buffer: Buffer containing the video file (from st.file_uploader).
        num_frames_to_extract (int): Total number of frames to extract evenly spaced.
        frame_rate_to_sample (int): Original FPS to sample from, to avoid too many similar frames.

    Returns:
        list: A list of extracted frames (NumPy arrays - RGB).
              Returns empty list if video can't be opened or not enough frames.
    """
    extracted_frames = []
    # Streamlit's UploadedFile is a buffer. We need to write it to a temporary file
    # for OpenCV to open it.
    with tempfile.NamedTemporaryFile(delete=False, suffix=".mp4") as tmp_file:
        tmp_file.write(video_file_buffer.read())
        video_path = tmp_file.name

    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        st.error("Error: Could not open video file.")
        os.unlink(video_path) # Clean up temp file
        return extracted_frames

    total_frames_in_video = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    video_fps = cap.get(cv2.CAP_PROP_FPS)
    
    if total_frames_in_video == 0:
        st.warning("Video has no frames or metadata is unreadable.")
        cap.release()
        os.unlink(video_path)
        return extracted_frames

    # Calculate which frames to pick, attempting to get them evenly spaced
    # And also considering the frame_rate_to_sample to avoid picking too many frames if video is short
    # We want 'num_frames_to_extract' frames.
    # If we sample at 'frame_rate_to_sample' FPS, the video has effectively total_frames_in_video / video_fps * frame_rate_to_sample potential frames
    
    # Simplified: pick frames evenly from the video.
    if num_frames_to_extract > total_frames_in_video:
        st.warning(f"Requested {num_frames_to_extract} frames, but video only has {total_frames_in_video}. Using all available frames.")
        frame_indices = range(total_frames_in_video)
    else:
        frame_indices = np.linspace(0, total_frames_in_video - 1, num_frames_to_extract, dtype=int)

    count = 0
    processed_indices_count = 0
    while cap.isOpened() and processed_indices_count < len(frame_indices):
        ret, frame = cap.read()
        if not ret:
            break
        if count in frame_indices:
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB) # Convert to RGB
            extracted_frames.append(frame_rgb)
            processed_indices_count +=1
        count += 1
    
    cap.release()
    os.unlink(video_path) # Clean up temp file
    return extracted_frames

def preprocess_frame_for_model(frame_rgb):
    """
    Preprocesses a single frame (RGB NumPy array) for the DeepfakeDetector model.
    (Resize, ToTensor, Normalize - similar to training)
    """
    transform_pipeline = transforms.Compose([
        transforms.ToPILImage(),
        transforms.Resize((224, 224)), # EfficientNet-B0 expects 224x224
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    return transform_pipeline(frame_rgb)

# --- Model Loading ---
@st.cache_resource # Cache the model to avoid reloading on every interaction
def load_deepfake_model(model_path):
    """Loads the DeepfakeDetector model."""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model = DeepfakeDetector(num_classes=1) # pretrained=True by default in our model.py
    
    if hasattr(model, 'dummy') and model.dummy:
        st.warning("Using a DUMMY model. Predictions will be random. "
                   "Place your trained 'deepfake_detector_best.pth' in the 'models' folder.")
        return model.to(device), True # True indicates dummy model

    if not os.path.exists(model_path):
        st.warning(f"Model weights not found at {model_path}. "
                   "The model will use its initial pre-trained EfficientNet weights (if any) or random weights. "
                   "Place your trained 'deepfake_detector_best.pth' in the 'models' folder for actual deepfake detection.")
        # Even if weights are not found, the model uses pretrained EfficientNet weights by default from our model.py
        # So it's not purely "random" unless pretrained=False was set.
        return model.to(device), False # False indicates real model, but possibly without fine-tuning

    try:
        model.load_state_dict(torch.load(model_path, map_location=device))
        st.success(f"Model loaded successfully from {model_path} on {device}.")
    except Exception as e:
        st.error(f"Error loading model weights from {model_path}: {e}. "
                 "The model will use its initial pre-trained EfficientNet weights.")
        return model.to(device), False # Real model, but failed to load fine-tuned weights
        
    model.eval()
    return model.to(device), False # Real model, successfully loaded fine-tuned weights

# --- Main Application ---
def main():
    st.set_page_config(page_title="Deepfake Video Detector", layout="wide")
    st.title("🎬 Deepfake Video Detector")
    st.markdown("""
        Upload a video file to check if it's a deepfake. 
        The model will process a few frames, make a prediction, and show Grad-CAM explanations.
        
        **Note:** This is a demonstration application. Performance depends heavily on the underlying model's training.
        A dummy model or a model with only base EfficientNet weights might be in use if a fine-tuned checkpoint is not found.
    """)

    # --- Sidebar for Controls ---
    st.sidebar.header("Controls")
    num_frames_process = st.sidebar.slider(
        "Number of frames to process for prediction:", 
        min_value=1, max_value=20, value=DEFAULT_NUM_FRAMES_TO_PROCESS,
        help="More frames give a more robust video-level prediction but take longer."
    )
    num_frames_xai = st.sidebar.slider(
        "Number of frames for XAI (Grad-CAM):", 
        min_value=1, max_value=10, value=DEFAULT_NUM_FRAMES_FOR_XAI,
        help="Number of frames to show Grad-CAM explanations for."
    )

    # --- Load Model ---
    # This is done once and cached.
    model, is_dummy_model = load_deepfake_model(MODEL_PATH)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # --- Video Upload ---
    uploaded_file = st.file_uploader("Choose a video file (.mp4, .avi, .mov)", type=["mp4", "avi", "mov"])

    if uploaded_file is not None:
        st.video(uploaded_file)

        if st.button("Analyze Video"):
            with st.spinner(f"Analyzing video... Extracting {num_frames_process} frames..."):
                # 1. Extract Frames
                video_frames_rgb = extract_frames_from_video(uploaded_file, num_frames_process)

            if not video_frames_rgb:
                st.error("Could not extract frames from the video. Please try another file.")
                return

            st.success(f"Successfully extracted {len(video_frames_rgb)} frames for processing.")
            
            frame_predictions = []
            processed_frame_tensors = []

            with st.spinner("Preprocessing frames and getting predictions..."):
                for frame_rgb in video_frames_rgb:
                    # 2. Preprocess Frame
                    input_tensor = preprocess_frame_for_model(frame_rgb).unsqueeze(0).to(device)
                    processed_frame_tensors.append(input_tensor)
                    
                    # 3. Get Prediction for Frame
                    with torch.no_grad():
                        output = model(input_tensor)
                        prob = torch.sigmoid(output).item() # Probability of being fake
                    frame_predictions.append(prob)
            
            if not frame_predictions:
                st.error("No predictions were made. Something went wrong during processing.")
                return

            # 4. Aggregate to Video-Level Prediction
            avg_fake_probability = np.mean(frame_predictions)
            video_prediction_label = "Deepfake" if avg_fake_probability > 0.5 else "Real"
            confidence = avg_fake_probability if video_prediction_label == "Deepfake" else 1 - avg_fake_probability

            st.subheader("Overall Video Prediction")
            if video_prediction_label == "Deepfake":
                st.error(f"**Status: {video_prediction_label}** (Confidence: {confidence:.2%})")
            else:
                st.success(f"**Status: {video_prediction_label}** (Confidence: {confidence:.2%})")
            
            if is_dummy_model:
                st.warning("Reminder: A DUMMY model is being used. The prediction above is random.")

            # 5. XAI Visualization (Grad-CAM)
            st.subheader("Explainable AI (XAI) - Grad-CAM Heatmaps")
            st.markdown(f"Showing Grad-CAM for {min(num_frames_xai, len(video_frames_rgb))} representative frames.")
            st.markdown("_Heatmaps highlight regions the model focused on for its decision. Red areas indicate higher importance._")

            if 'explainability' not in sys.modules or GradCAM is None: # Check if explainability module was loaded
                 st.warning("Grad-CAM functionality is not available (likely due to import errors for 'explainability.py' or 'pytorch_grad_cam').")
            elif hasattr(model, 'dummy') and model.dummy:
                st.warning("Grad-CAM cannot be generated for the DUMMY model.")
            else:
                # Select frames for XAI - e.g., evenly spaced from the processed ones
                xai_frame_indices = np.linspace(0, len(video_frames_rgb) - 1, 
                                                min(num_frames_xai, len(video_frames_rgb)), dtype=int)
                
                # Determine target layer for Grad-CAM
                try:
                    # For our DeepfakeDetector with EfficientNet-B0, the target is usually the last conv block
                    target_layer = model.efficientnet.features[-1]
                except AttributeError:
                    st.error("Could not identify target layer for Grad-CAM in the model. "
                             "Ensure the model has an 'efficientnet.features' attribute structure.")
                    return # Stop if we can't get the layer

                cols = st.columns(min(num_frames_xai, len(video_frames_rgb)))
                for i, frame_idx in enumerate(xai_frame_indices):
                    original_frame_rgb = video_frames_rgb[frame_idx]
                    input_tensor_for_xai = processed_frame_tensors[frame_idx] # Already on device

                    with st.spinner(f"Generating Grad-CAM for frame {frame_idx+1}..."):
                        # Generate Grad-CAM (target_category=0 for the single output logit)
                        # For a single output logit (fake probability), target_category=0 refers to that output.
                        grayscale_cam = generate_grad_cam_heatmap(model, target_layer, input_tensor_for_xai, target_category=0) 
                        
                        if grayscale_cam is None:
                            cols[i].error(f"Failed to generate Grad-CAM for frame {frame_idx+1}.")
                            continue

                        # Overlay heatmap on the original frame (before normalization)
                        # visualize_grad_cam expects normalized tensor, but we can adapt or use original
                        # For simplicity, let's re-create the visual input for show_cam_on_image from original_frame_rgb
                        
                        # Re-normalize original_frame_rgb to be in [0,1] float for show_cam_on_image
                        display_frame = original_frame_rgb.astype(np.float32) / 255.0
                        visualization = show_cam_on_image(display_frame, grayscale_cam, use_rgb=True)

                    with cols[i]:
                        st.image(visualization, caption=f"Frame {frame_idx+1} | Fake Prob: {frame_predictions[frame_idx]:.2f}", use_column_width=True)
    else:
        st.info("Upload a video file to get started.")

    st.sidebar.markdown("---")
    st.sidebar.markdown("**About**")
    st.sidebar.info(
        "This app uses a deep learning model (EfficientNet-B0 based) "
        "to detect deepfakes in videos. Grad-CAM is used for explainability."
        "\n\n"
        "**Disclaimer:** For educational/demonstration purposes only."
    )

if __name__ == "__main__":
    """
    To run this Streamlit application:
    1. Ensure you have Python and pip installed.
    2. Install necessary libraries:
       pip install streamlit torch torchvision opencv-python numpy Pillow matplotlib grad-cam
    3. Make sure you have the following project files in the 'src' subdirectory:
       - src/model.py (containing DeepfakeDetector class)
       - src/explainability.py (containing Grad-CAM functions)
    4. (Optional but Recommended) Place your trained model weights at:
       deepfake_detector/models/deepfake_detector_best.pth
    5. Open your terminal, navigate to the directory containing THIS `app.py` file (i.e., the `deepfake_detector` root).
    6. Run the command:
       streamlit run app.py
    """
    main()
```
