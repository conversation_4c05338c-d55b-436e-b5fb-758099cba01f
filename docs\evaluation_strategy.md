# Deepfake Video Detector: Evaluation and Testing Strategy

This document outlines the comprehensive strategy for evaluating and testing the deepfake video detection model, as per Phase 5. It covers performance metrics, testing protocols, robustness checks, and validation of XAI explanations.

## 1. Performance Metrics

For evaluating our binary classification model (real vs. fake), the following key performance metrics will be used. These can be calculated using libraries like `scikit-learn` based on the model's predictions on the test set.

*   **Accuracy:**
    *   **Formula:** `(True Positives + True Negatives) / (Total Samples)`
    *   **Relevance:** Represents the overall correctness of the model. While intuitive, it can be misleading if the dataset is imbalanced (e.g., significantly more real videos than fake ones, or vice-versa).
    *   **Calculation:** `sklearn.metrics.accuracy_score(y_true, y_pred)`

*   **Precision (for the 'fake' class):**
    *   **Formula:** `True Positives / (True Positives + False Positives)`
        *   *True Positives (TP):* Fake videos correctly identified as fake.
        *   *False Positives (FP):* Real videos incorrectly identified as fake.
    *   **Relevance:** Measures the proportion of videos flagged as 'fake' that are actually fake. High precision is crucial when the cost of a false positive (e.g., flagging a real video as fake) is high.
    *   **Calculation:** `sklearn.metrics.precision_score(y_true, y_pred, pos_label=1)` (assuming '1' is the label for 'fake')

*   **Recall (Sensitivity, True Positive Rate - for the 'fake' class):**
    *   **Formula:** `True Positives / (True Positives + False Negatives)`
        *   *False Negatives (FN):* Fake videos incorrectly identified as real.
    *   **Relevance:** Measures the proportion of actual fake videos that the model correctly identifies. High recall is important when the cost of a false negative (missing a deepfake) is high.
    *   **Calculation:** `sklearn.metrics.recall_score(y_true, y_pred, pos_label=1)`

*   **F1-Score (for the 'fake' class):**
    *   **Formula:** `2 * (Precision * Recall) / (Precision + Recall)`
    *   **Relevance:** The harmonic mean of precision and recall. It provides a single score that balances both concerns. It's particularly useful when there's an uneven class distribution or when both false positives and false negatives are important.
    *   **Calculation:** `sklearn.metrics.f1_score(y_true, y_pred, pos_label=1)`

*   **Area Under the Receiver Operating Characteristic Curve (AUC-ROC):**
    *   **Concept:** The ROC curve plots the True Positive Rate (Recall) against the False Positive Rate (`False Positives / (False Positives + True Negatives)`) at various threshold settings. The AUC represents the likelihood of the model ranking a random positive instance higher than a random negative instance.
    *   **Relevance:** Provides a measure of the model's ability to distinguish between the two classes across all possible classification thresholds. It's threshold-independent and useful for evaluating the model's overall discriminative power. An AUC of 0.5 suggests no discriminative ability (random guessing), while 1.0 indicates perfect discrimination.
    *   **Calculation:** Requires predicted probabilities for the 'fake' class. `sklearn.metrics.roc_auc_score(y_true, y_pred_probabilities)`

*   **Confusion Matrix:**
    *   **Concept:** A table showing the number of TPs, FPs, TNs (True Negatives: real videos correctly identified as real), and FNs.
    *   **Relevance:** Provides a detailed breakdown of correct and incorrect classifications for each class, helping to identify specific error patterns.
    *   **Calculation:** `sklearn.metrics.confusion_matrix(y_true, y_pred)`

**Reporting:**
These metrics should be calculated on the predictions made by the final trained model on the held-out **test set**. Results should be clearly tabulated. For video-level predictions (aggregated from frame predictions), ensure `y_true` and `y_pred` reflect the video labels.

## 2. Testing Protocol

The final evaluation of the deepfake detection model will be performed using the dedicated **test set**, which was set aside during Phase 1 (Data Preparation).

**Key Principles:**

*   **Isolation of Test Set:** The test set must **not** be used during any part of the model development process, including:
    *   Training.
    *   Hyperparameter tuning (e.g., learning rate, batch size selection).
    *   Model architecture selection or refinement.
    *   Any form of feature engineering or data preprocessing strategy development that involves looking at the test data.
*   **Purpose:** The test set serves as an unbiased measure of the model's generalization performance on unseen data. Using it prematurely would lead to an overly optimistic estimate of its real-world performance.
*   **Procedure:**
    1.  **Complete Model Development:** Fully train the chosen model architecture (e.g., `DeepfakeDetector` with EfficientNet-B0) using the training data.
    2.  **Hyperparameter Optimization:** Tune hyperparameters using only the training and validation sets.
    3.  **Final Model Selection:** Select the best model based on its performance on the validation set.
    4.  **Single Evaluation on Test Set:** Once the model is finalized and all development is complete, run it **once** on the test set.
        *   The `preprocess.py` script will be used to extract frames from test videos.
        *   The `train.py` script's logic (or a separate `evaluate.py` script) will load the final model and process these frames to get predictions.
        *   Video-level predictions will be aggregated (e.g., by averaging frame probabilities or majority vote).
    5.  **Report Metrics:** Calculate and report all the performance metrics listed in Section 1 using the test set predictions.

## 3. Robustness Checks

Beyond standard performance metrics, it's important to assess how robust the model is to variations in input data.

*   **Performance on Videos with Varying Quality:**
    *   **Objective:** To understand if the model's performance degrades significantly with lower quality videos (common in real-world scenarios).
    *   **Method:**
        *   **If Data Available:** If the Deepfake Detection Challenge Dataset (or any other available dataset) contains videos with explicitly different quality metadata (compression levels, resolutions, lighting conditions), create subsets of the test set based on these characteristics and evaluate the model on each subset.
        *   **Simulated Data (Augmentation on Test Set - with caution):**
            *   If such varied data is not readily available, specific augmentations can be applied *to copies* of the test set frames to simulate these conditions. **Crucially, these augmentations must not be seen during training or validation to avoid data leakage.**
            *   Examples of augmentations for robustness testing:
                *   **Compression Artifacts:** Re-encode videos/frames with different JPEG compression levels or simulate H.264 compression artifacts.
                *   **Resolution Changes:** Downscale and then upscale frames (`cv2.resize`).
                *   **Brightness/Contrast Variations:** Systematically alter brightness and contrast.
                *   **Added Noise:** Apply Gaussian noise or other types of noise.
            *   Evaluate the model on these augmented test subsets and compare performance to the original test set. Significant drops indicate sensitivity to these variations.

*   **Adversarial Examples (Advanced/Optional):**
    *   **Concept:** Adversarial examples are inputs to a machine learning model that an attacker has intentionally designed to cause the model to make a mistake. For deepfake detection, this would mean a subtly modified deepfake video that the model classifies as 'real', or a modified 'real' video classified as 'fake'. The modifications are often imperceptible to humans.
    *   **Relevance:** Tests the model's vulnerability to deliberate attacks. While deepfake generation itself is an adversarial attack on visual perception, adversarial examples in this context refer to attacks on the *detector model*.
    *   **Method (Brief Overview):**
        *   Techniques like Fast Gradient Sign Method (FGSM) or Projected Gradient Descent (PGD) can be used to generate these examples.
        *   Requires access to model gradients.
        *   Specialized libraries (e.g., `ART - Adversarial Robustness Toolbox`, `CleverHans`) can assist.
    *   **Consideration:** This is an advanced topic, usually explored once a strong baseline model exists. It can be computationally intensive.

## 4. XAI Validation (Qualitative Assessment of Grad-CAM)

The Grad-CAM heatmaps generated by `explainability.py` (Phase 4) provide insights into *where* the model is looking when making a prediction. A qualitative validation is necessary to assess if these explanations are meaningful.

**Procedure:**

1.  **Sample Selection:**
    *   From the test set (or validation set if done during development), select a diverse range of samples for XAI validation:
        *   **True Positives (TPs):** Fake videos correctly identified.
        *   **False Positives (FPs):** Real videos incorrectly identified as fake.
        *   **True Negatives (TNs):** Real videos correctly identified.
        *   **False Negatives (FNs):** Fake videos incorrectly identified as real.
2.  **Generate Grad-CAM Heatmaps:** Use `explainability.py` to generate heatmaps for these selected samples. Target the final convolutional layer of the EfficientNet backbone (e.g., `model.efficientnet.features[-1]`).
3.  **Qualitative Review:**
    *   **Plausibility:**
        *   **TPs:** Do the heatmaps highlight regions of the face that are commonly associated with deepfake artifacts (e.g., around the eyes, mouth, facial contours, inconsistencies in lighting or texture)? Even if artifacts are not obvious to the naked eye, does the model focus on facial areas?
        *   **TNs:** For real videos, are the heatmaps diffuse, or do they focus on natural facial features without indicating specific "suspicious" regions?
    *   **Identifying Failure Modes (FPs & FNs):**
        *   **FPs:** For real videos classified as fake, where is the model looking? Is it focusing on unusual lighting, shadows, or specific accessories that might be confusing it? This can reveal biases or spurious correlations learned by the model.
        *   **FNs:** For fake videos classified as real, are the heatmaps uninformative, or do they highlight areas that *should* have been suspicious but perhaps the artifacts were too subtle or of a type the model hasn't learned to detect well?
    *   **Consistency:** Are there consistent patterns in heatmaps for certain types of fakes or for specific failure cases?
    *   **Comparison:** Directly compare heatmaps for TPs vs. FPs, and TNs vs. FNs. This can help understand what drives correct vs. incorrect classifications. For instance, if TPs consistently focus on mouth/eye regions and FPs focus on background elements, it suggests the model might be distracted by irrelevant features in some cases.

**Documentation:**
Summarize the findings from the qualitative XAI validation. Include example heatmaps that illustrate key observations (e.g., successful localizations, misleading activations for FPs). This qualitative feedback can be valuable for further model refinement or for understanding the model's limitations.
