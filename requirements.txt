# Core Deep Learning & Computation
torch==2.7.0
torchvision==0.22.0
numpy==2.2.6 # As seen in grad-cam install
# Or a more general numpy, e.g., numpy>=1.20.0

# Image/Video Processing
opencv-python==4.11.0.86 # As seen in grad-cam install
Pillow==9.5.0 # Downgraded to resolve <PERSON><PERSON>'s pillow<11 requirement

# Machine Learning Utilities
scikit-learn==1.6.1 # As seen in grad-cam install

# Visualization & UI
matplotlib==3.10.3 # As seen in grad-cam install
streamlit==1.37.0 # A recent version of Streamlit, adjust if specific version needed
# Note: Streamlit has its own dependencies, usually handled by its installation.

# Explainable AI
grad-cam==1.5.5 # As seen in grad-cam install
# ttach==0.0.3 was a dependency of grad-cam

# Utilities
tqdm==4.67.1 # As seen in grad-cam install (often used for progress bars)

# Other dependencies that were pulled in by torch/torchvision/grad-cam
# These are often transitive dependencies but listing key ones can be helpful.
# Consider if all of these are strictly necessary or if top-level ones suffice.
# For a minimal requirements.txt, one might only list torch, torchvision, opencv-python, streamlit, scikit-learn, matplotlib, grad-cam.
# However, for reproducibility, more specific versions pulled during a successful install are better.

# Dependencies from the last successful 'pip install grad-cam' output:
# (Excluding the very low-level nvidia-* packages as they are specific to CUDA build)
contourpy>=1.0.1 # from matplotlib
cycler>=0.10 # from matplotlib
fonttools>=4.22.0 # from matplotlib
kiwisolver>=1.3.1 # from matplotlib
packaging>=20.0 # from matplotlib
python-dateutil>=2.7 # from matplotlib
# six>=1.5 # very common, usually present
mpmath>=1.1.0 # from sympy (torch dependency)
sympy>=1.13.3 # from torch
networkx # from torch
jinja2 # from torch
fsspec # from torch
# For scikit-learn:
joblib>=1.2.0
threadpoolctl>=3.1.0
scipy>=1.6.0 # also a numpy dependency
# ttach (already listed as grad-cam dependency)
MarkupSafe>=2.0 # from jinja2
