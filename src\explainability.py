import torch
import cv2
import numpy as np
import matplotlib.pyplot as plt
from torchvision import transforms
from PIL import Image

# Attempt to import pytorch_grad_cam and related utilities
try:
    from pytorch_grad_cam import GradCAM
    from pytorch_grad_cam.utils.image import show_cam_on_image
    from pytorch_grad_cam.utils.model_targets import ClassifierOutputTarget
except ImportError:
    print("Error: pytorch-grad-cam library not found. Please install it: pip install grad-cam")
    # Provide dummy implementations or raise error if essential
    GradCAM = None 
    show_cam_on_image = None
    ClassifierOutputTarget = None

# Import our model
try:
    from model import DeepfakeDetector # Assuming model.py is in the same directory or PYTHONPATH
except ImportError:
    print("Error: Could not import DeepfakeDetector from model.py. Ensure it's in the correct path.")
    # Provide a dummy model class if DeepfakeDetector cannot be imported, for basic script structure.
    class DeepfakeDetector(torch.nn.Module):
        def __init__(self, num_classes=1, pretrained=False): # Added pretrained for compatibility
            super().__init__()
            # Using a simple convnet as a placeholder if EfficientNet is not available or model.py is missing
            self.features = torch.nn.Sequential(torch.nn.Conv2d(3, 8, 3, padding=1), torch.nn.ReLU(), torch.nn.AdaptiveAvgPool2d((1,1)))
            self.classifier = torch.nn.Linear(8, num_classes)
            print("Warning: Using a dummy DeepfakeDetector model for explainability.py.")
        def forward(self, x):
            x = self.features(x)
            x = x.view(x.size(0), -1)
            return self.classifier(x)

# --- Grad-CAM Generation Function ---
def generate_grad_cam_heatmap(model, target_layer, input_tensor, target_category=None):
    """
    Generates a Grad-CAM heatmap for a given model and input.

    Args:
        model (torch.nn.Module): The trained PyTorch model.
        target_layer (torch.nn.Module): The target convolutional layer to generate CAM for.
                                         e.g., model.efficientnet.features[-1][0] for EfficientNet's last conv block.
        input_tensor (torch.Tensor): Preprocessed input image tensor (batch_size=1, C, H, W).
        target_category (int, optional): Target category for CAM. For binary classification,
                                         0 for 'real', 1 for 'fake'. If None, the highest scoring
                                         category will be used.

    Returns:
        np.ndarray: The CAM heatmap (grayscale_cam), normalized to [0, 1].
                    Returns None if GradCAM library is not available.
    """
    if GradCAM is None:
        print("GradCAM library is not available. Cannot generate heatmap.")
        return None

    # Construct the CAM object once and use it multiple times
    # `use_cuda` should be set based on whether the model and input_tensor are on GPU
    use_cuda = input_tensor.is_cuda
    cam = GradCAM(model=model, target_layers=[target_layer], use_cuda=use_cuda)

    # Define targets for binary classification
    # If target_category is None, it will use the predicted class.
    # For binary (real/fake), if output is a single logit:
    #   - target_category=0 might mean "more real"
    #   - target_category=1 might mean "more fake"
    # This depends on how your labels are defined (e.g., 0=real, 1=fake)
    # The ClassifierOutputTarget expects a list of target categories.
    targets = None
    if target_category is not None:
        targets = [ClassifierOutputTarget(target_category)]

    # You can also pass aug_smooth=True and eigen_smooth=True for smoother CAMs
    grayscale_cam = cam(input_tensor=input_tensor, targets=targets)

    # In this example grayscale_cam has only one image in the batch
    # If you have multiple images, grayscale_cam will be a numpy array batch
    grayscale_cam_single = grayscale_cam[0, :]
    
    return grayscale_cam_single

def visualize_grad_cam(input_tensor, grayscale_cam, save_path=None):
    """
    Overlays the Grad-CAM heatmap on the original image and displays it.

    Args:
        input_tensor (torch.Tensor): The original input image tensor (1, C, H, W).
                                     Expected to be normalized if the model expects normalized input.
        grayscale_cam (np.ndarray): The Grad-CAM heatmap.
        save_path (str, optional): Path to save the visualization. If None, displays using matplotlib.
    """
    if show_cam_on_image is None:
        print("GradCAM utility 'show_cam_on_image' is not available. Cannot visualize.")
        return

    # Convert input_tensor to a displayable RGB image (0-1 range, HWC format)
    # This assumes the input_tensor was normalized. We need to unnormalize or at least clip.
    # For simplicity, we'll assume it's [-1, 1] or [0, 1] and try to make it a viewable image.
    img = input_tensor.squeeze(0).cpu().detach().numpy() # Remove batch dim, move to CPU, convert to numpy
    img = np.transpose(img, (1, 2, 0)) # CHW to HWC

    # Unnormalize (example for ImageNet normalization) - adjust if your normalization is different
    mean = np.array([0.485, 0.456, 0.406])
    std = np.array([0.229, 0.224, 0.225])
    img = std * img + mean
    img = np.clip(img, 0, 1) # Clip values to be between 0 and 1

    # Overlay CAM
    # use_rgb=True is important for color images
    visualization = show_cam_on_image(img, grayscale_cam, use_rgb=True)

    # Display or save
    if save_path:
        plt.imsave(save_path, visualization)
        print(f"Grad-CAM visualization saved to {save_path}")
    else:
        plt.imshow(visualization)
        plt.title("Grad-CAM Overlay")
        plt.axis('off')
        plt.show()

# --- Integration with Training/Evaluation (Comments) ---
#
# How to use Grad-CAM for model evaluation and analysis:
#
# 1. Identify Target Layers:
#    - Before evaluation, identify the convolutional layer in your model (e.g., `DeepfakeDetector`)
#      that you want to visualize activations from. For EfficientNet-B0 within `DeepfakeDetector`,
#      this is typically the last convolutional block in `model.efficientnet.features`.
#      Example: `target_layer = model.efficientnet.features[-1]` or a specific conv layer within it.
#
# 2. Hook into Evaluation Loop:
#    - During your validation/testing loop (e.g., in `validate_one_epoch` in `train.py`),
#      after getting predictions for a batch of validation samples:
#      ```python
#      # Inside validation loop in train.py, after:
#      # outputs = model(inputs)
#      # preds = torch.sigmoid(outputs) > 0.5
#
#      # For some samples (e.g., misclassified ones or randomly selected ones):
#      for i in range(inputs.size(0)): # Iterate through batch
#          if (preds[i] != labels[i]) or (random.random() < 0.05): # Example: misclassified or 5% of samples
#              input_frame_tensor = inputs[i].unsqueeze(0) # Get single frame, add batch dim
#              true_label = labels[i].item()
#              pred_label = preds[i].item()
#
#              # Ensure model is on the same device as input_frame_tensor
#              # model.to(DEVICE) # model should already be on DEVICE
#
#              # Select the target layer from your model instance
#              # This might require inspecting the model structure.
#              # For DeepfakeDetector using efficientnet_b0:
#              # target_layer_candidate = model.efficientnet.features[-1] # This is a Sequential block
#              # Often, the actual conv layer is inside, e.g., model.efficientnet.features[-1][0]
#              # Or, for a simpler model: target_layer = model.features[-1] (if it's a conv layer)
#              try:
#                  # For torchvision EfficientNet-B0, the last conv block is model.features[-1]
#                  # The actual conv layer might be the first element of this block if it's an MBConv block
#                  # For example: model.efficientnet.features[-1][0].block[0][0] (very specific)
#                  # A common choice is the last conv layer before global average pooling.
#                  # For efficientnet_b0, model.efficientnet.features as a whole can work if the library
#                  # can find the last conv layer, or model.efficientnet.features[-1] (the last MBConv block)
#                  # Or more robustly:
#                  target_layer = model.efficientnet.features[-1] # The last MBConv block
#                  # Or even deeper if that block contains nested conv layers:
#                  # e.g. model.efficientnet.features[-1][0].conv # if structure is known
#              except AttributeError:
#                  print("Could not find default target layer for EfficientNet in the model. Please specify manually.")
#                  continue # Skip if layer not found
#
#              heatmap = generate_grad_cam_heatmap(model, target_layer, input_frame_tensor, target_category=int(pred_label))
#              if heatmap is not None:
#                  # Create a unique filename
#                  filename = f"grad_cam_epoch{epoch}_sample{i}_true{true_label}_pred{pred_label}.png"
#                  output_dir = "deepfake_detector/data/grad_cam_outputs"
#                  os.makedirs(output_dir, exist_ok=True) # Ensure directory exists
#                  save_filepath = os.path.join(output_dir, filename)
#                  visualize_grad_cam(input_frame_tensor, heatmap, save_path=save_filepath)
#      ```
#
# 3. Analysis:
#    - Review saved heatmaps.
#    - For correct predictions: Does the model focus on relevant facial features or artifacts?
#    - For incorrect predictions: Where does the model look? Does it focus on irrelevant background
#      details or get confused by specific patterns? This can provide insights into model biases
#      or weaknesses.
#    - Compare heatmaps for real vs. fake images. Are there systematic differences in highlighted regions?

# --- SHAP (Brief Mention) ---
#
# SHAP (SHapley Additive exPlanations) is another powerful XAI technique.
# - It provides more precise pixel-level (or feature-level) attribution values,
#   based on game theory concepts (Shapley values).
# - SHAP can explain the contribution of each feature (e.g., pixel or superpixel)
#   to the model's prediction for a specific instance.
# - For vision models, especially complex ones like CNNs, SHAP can be:
#   - More computationally intensive than Grad-CAM, as it often requires multiple
#     forward passes through portions of the model for different "coalitions" of features.
#   - More complex to set up (e.g., using `shap.GradientExplainer` or `shap.DeepExplainer`
#     for deep learning models). It might require careful handling of input baselines.
# - While Grad-CAM gives a class-discriminative localization map, SHAP aims for a more
#   detailed attribution of how each part of the input contributes to the deviation
#   from a baseline prediction.
# - Tools: The `shap` library (https://github.com/slundberg/shap).
#

# --- Example Usage ---
if __name__ == '__main__':
    if GradCAM is None or show_cam_on_image is None:
        print("pytorch-grad-cam is not installed or not working. Aborting example.")
    else:
        print("Running Grad-CAM example...")
        
        # 1. Initialize Model (ensure model.py is accessible)
        # Use a dummy model for this example if the full model isn't needed or available
        # For a real scenario, load your trained model weights.
        device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        # model = DeepfakeDetector(num_classes=1, pretrained=False) # Use pretrained=False for fresh instance
        
        # More robustly load the model, assuming it's the one from the project
        try:
            model = DeepfakeDetector(num_classes=1) # pretrained=True by default in DeepfakeDetector
            print("Successfully loaded DeepfakeDetector from model.py")
        except Exception as e:
            print(f"Error loading DeepfakeDetector from model.py: {e}")
            print("Using a placeholder model for the example.")
            # Fallback to a very simple model for demonstration if the main model fails
            model = torch.nn.Sequential(
                torch.nn.Conv2d(3, 8, 3, padding=1), torch.nn.ReLU(),
                torch.nn.Conv2d(8, 8, 3, padding=1), torch.nn.ReLU(), # Added another conv layer
                torch.nn.AdaptiveAvgPool2d((1,1)),
                torch.nn.Flatten(),
                torch.nn.Linear(8, 1)
            ).to(device)
            # For this simple model, target layer could be model[2] (the second Conv2d)
            # We need to define 'efficientnet' attribute for the target_layer logic below to work, or adjust it
            class DummyWrapper(torch.nn.Module): # Wrapper to mimic efficientnet structure for target layer
                def __init__(self, seq_model):
                    super().__init__()
                    self.efficientnet = torch.nn.Module()
                    self.efficientnet.features = torch.nn.Sequential(*list(seq_model.children())[:-3]) # Conv layers
                    self.classifier_replacement = torch.nn.Sequential(*list(seq_model.children())[-3:]) # Pool, Flatten, Linear
                def forward(self,x):
                    x = self.efficientnet.features(x)
                    return self.classifier_replacement(x)
            model = DummyWrapper(model)


        model.eval() # Set model to evaluation mode
        model.to(device)

        # 2. Prepare Input Tensor
        # Create a dummy RGB image (e.g., 224x224 for EfficientNet-B0)
        # In a real scenario, load and preprocess an actual image.
        try:
            # Try to create a random image and apply typical ImageNet normalization
            transform = transforms.Compose([
                transforms.ToPILImage(), # If starting from numpy
                transforms.Resize((224, 224)),
                transforms.ToTensor(),
                transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
            ])
            dummy_np_image = (np.random.rand(224, 224, 3) * 255).astype(np.uint8)
            input_tensor = transform(dummy_np_image).unsqueeze(0).to(device) # Add batch dim
        except Exception as e:
            print(f"Error creating dummy input tensor: {e}. Using pure random tensor.")
            input_tensor = torch.randn(1, 3, 224, 224).to(device)


        # 3. Identify Target Layer
        # For EfficientNet-B0 in our DeepfakeDetector, the features are in `model.efficientnet.features`
        # The last convolutional block is `model.efficientnet.features[-1]`.
        # The `pytorch-grad-cam` library is often good at finding the last conv layer if you give it a block.
        try:
            # This is a common target for EfficientNet like models.
            # It usually refers to the last stack of MBConvBlocks.
            target_layer = model.efficientnet.features[-1]
            # Test if target_layer is valid
            if not isinstance(target_layer, torch.nn.Module):
                 raise ValueError("Identified target layer is not a torch.nn.Module")
            print(f"Using target layer: {type(target_layer)}")

        except AttributeError as e:
            print(f"Error: Could not find 'model.efficientnet.features[-1]'. This model might not be the expected EfficientNet. Error: {e}")
            print("Please adjust 'target_layer' based on your model's structure. Trying a generic fallback if possible.")
            # Fallback for a simple sequential model if the above fails (e.g. the dummy model)
            try:
                # Attempt to find any Conv2d layer as a last resort
                found_conv = False
                for i in range(len(model.features) -1, -1, -1): # Iterate backwards
                    if isinstance(model.features[i], torch.nn.Conv2d):
                        target_layer = model.features[i]
                        print(f"Fallback: Using target layer: {type(target_layer)}")
                        found_conv = True
                        break
                if not found_conv:
                    print("Fallback failed: No Conv2d layer found in model.features. Cannot run Grad-CAM.")
                    target_layer = None
            except AttributeError: # If model doesn't even have model.features
                print("Fallback failed: Model does not have 'features'. Cannot run Grad-CAM.")
                target_layer = None
        
        if target_layer:
            # 4. Generate Grad-CAM
            # For binary classification with a single output logit:
            # target_category=0 might mean "more real"
            # target_category=1 might mean "more fake"
            # If None, it uses the model's highest scoring class.
            # Let's assume 1 = fake (the class we want to explain if predicted)
            # Or, if the model output is > 0 for 'fake', then we can use category 0 if the output is a single logit.
            # The library handles single logit outputs for binary classification well if targets=None.
            
            # Let's make a forward pass to see what the model predicts for the dummy input
            with torch.no_grad():
                output_logit = model(input_tensor)
                prob = torch.sigmoid(output_logit).item()
            print(f"Model output logit: {output_logit.item():.4f}, Probability (fake): {prob:.4f}")

            # Generate CAM for the "fake" class (assuming output > 0 means fake)
            # If model outputs a single logit, ClassifierOutputTarget(0) or ClassifierOutputTarget(1) might seem confusing.
            # It's often easier to let targets=None for binary cases, or use a wrapper if the library expects specific indices.
            # For pytorch-grad-cam, with a single output logit, `targets=None` usually works fine.
            # Or, if you want to explain why it thinks it's class 0 (e.g. "real") or class 1 ("fake")
            # and your model output is a single logit where positive means fake, negative means real:
            # - To see why it's "fake" (positive logit), you can use ClassifierOutputTarget(0) IF the library
            #   interprets this as "the 0-th output index", which IS our single logit.
            # - If the model had 2 outputs (one for real, one for fake), then you'd use target_category=0 or 1.
            
            # Let's try with target_category=None first, which is usually robust.
            print("Generating Grad-CAM with target_category=None (model's prediction)")
            grayscale_cam_pred = generate_grad_cam_heatmap(model, target_layer, input_tensor, target_category=None)

            if grayscale_cam_pred is not None:
                visualize_grad_cam(input_tensor, grayscale_cam_pred, save_path="deepfake_detector/data/grad_cam_pred_example.png")
                print("Saved Grad-CAM for model's prediction.")

            # Example: Forcing explanation for "fake" (assuming class index 0 if single logit, or if your model setup uses 0 for 'fake')
            # This part can be tricky with single-logit binary output.
            # If model outputs 1 value, `ClassifierOutputTarget(0)` refers to that output.
            # If your model was `nn.Linear(num_ftrs, 2)` (for 2 classes), then `ClassifierOutputTarget(1)` would be for the "fake" class if it's index 1.
            # Since our model is `nn.Linear(num_ftrs, 1)`, `ClassifierOutputTarget(0)` targets this single output.
            # The "meaning" (real/fake) depends on how you interpret this logit (e.g. >0 is fake).
            
            print("\nGenerating Grad-CAM explicitly for the single output logit (target_category=0)")
            grayscale_cam_forced = generate_grad_cam_heatmap(model, target_layer, input_tensor, target_category=0) # Target the 0-th (and only) output logit

            if grayscale_cam_forced is not None:
                # Create dummy output directory if it doesn't exist
                import os
                os.makedirs("deepfake_detector/data", exist_ok=True)
                visualize_grad_cam(input_tensor, grayscale_cam_forced, save_path="deepfake_detector/data/grad_cam_forced_example.png")
                print("Saved Grad-CAM for forced target category.")
        else:
            print("No suitable target layer found. Skipping Grad-CAM generation.")

        print("\nGrad-CAM example finished.")
        print("Check 'deepfake_detector/data/' for output images (if successful).")

```
