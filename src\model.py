import torch
import torch.nn as nn
import torchvision.models as models

class DeepfakeDetector(nn.Module):
    """
    Deepfake detection model using a pre-trained EfficientNet-B0.
    """
    def __init__(self, num_classes=1, pretrained=True):
        """
        Initializes the model.

        Args:
            num_classes (int): Number of output classes (1 for binary classification - real/fake).
            pretrained (bool): Whether to load pre-trained weights for EfficientNet-B0.
        """
        super(DeepfakeDetector, self).__init__()
        
        # Load EfficientNet-B0
        # The choice of EfficientNet-B0 is based on its balance of accuracy and computational efficiency.
        # It's a modern architecture that often outperforms older models like ResNet50 with fewer parameters.
        self.efficientnet = models.efficientnet_b0(pretrained=pretrained)
        
        # The original classifier in EfficientNet-B0 has:
        # (classifier): Sequential(
        #   (0): Dropout(p=0.2, inplace=True)
        #   (1): Linear(in_features=1280, out_features=1000, bias=True)
        # )
        # We need to replace it for our binary classification task.
        num_ftrs = self.efficientnet.classifier[1].in_features
        
        # Replace the classifier with a new one for binary classification (real/fake)
        # It will output a single logit. A sigmoid function will be applied later (e.g., in loss or for prediction)
        self.efficientnet.classifier = nn.Sequential(
            nn.Dropout(p=0.2, inplace=True), # Using the same dropout as original
            nn.Linear(num_ftrs, num_classes)
        )

    def forward(self, x):
        """
        Forward pass of the model.

        Args:
            x (torch.Tensor): Input tensor (batch of image frames). 
                              Shape: (batch_size, channels, height, width)
        
        Returns:
            torch.Tensor: Output tensor (logits). Shape: (batch_size, num_classes)
        """
        return self.efficientnet(x)

if __name__ == '__main__':
    # Example usage:
    # Create a dummy input tensor (batch_size=4, channels=3, height=224, width=224)
    # EfficientNet-B0 expects input size of 224x224
    dummy_input = torch.randn(4, 3, 224, 224) 
    
    # Initialize the model
    model = DeepfakeDetector(num_classes=1) # num_classes=1 for binary (real/fake)
    
    # Get model output
    output = model(dummy_input)
    
    print("Model initialized successfully.")
    print(f"Input shape: {dummy_input.shape}")
    print(f"Output shape: {output.shape}") # Should be (4, 1)
    
    # To get probabilities, you would typically pass the output through a sigmoid function
    probabilities = torch.sigmoid(output)
    print(f"Probabilities shape: {probabilities.shape}")
    print(f"Example probabilities: {probabilities[:2]}")

    # Check if CUDA is available and move the model to GPU
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)
    dummy_input = dummy_input.to(device)
    output = model(dummy_input)
    print(f"\nModel moved to {device}.")
    print(f"Output shape on {device}: {output.shape}")

    # Further notes:
    # - Data Preprocessing: Input images need to be normalized similar to how ImageNet images were normalized
    #   when EfficientNet was pre-trained. Typical normalization for ImageNet models:
    #   mean = [0.485, 0.456, 0.406]
    #   std = [0.229, 0.224, 0.225]
    #   These transformations should be applied in the DataLoader.
    #
    # - Video Data Handling: This model processes individual frames. For video classification,
    #   predictions from frames of a single video would be aggregated (e.g., averaging probabilities,
    #   majority vote).
    #
    # - Training: The `train.py` script will handle the training loop, loss calculation (BCEWithLogitsLoss),
    #   and optimization.
```
