import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset # Placeholder for actual DataLoader
import numpy as np
from sklearn.metrics import accuracy_score

# Assuming model.py is in the same directory or PYTHONPATH is set
from model import DeepfakeDetector 

# --- Configuration ---
LEARNING_RATE = 1e-4
BATCH_SIZE = 32 # Adjust based on GPU memory
NUM_EPOCHS = 10 # Start with a small number for testing
DEVICE = torch.device("cuda" if torch.cuda.is_available() else "cpu")

# --- Helper Functions ---

def train_one_epoch(model, dataloader, criterion, optimizer, device):
    """
    Trains the model for one epoch.
    """
    model.train()  # Set model to training mode
    running_loss = 0.0
    all_labels = []
    all_predictions = []

    for i, (inputs, labels) in enumerate(dataloader):
        inputs = inputs.to(device)
        labels = labels.to(device).float().unsqueeze(1) # Ensure labels are float and have shape [batch_size, 1]

        # Zero the parameter gradients
        optimizer.zero_grad()

        # Forward pass
        outputs = model(inputs)
        loss = criterion(outputs, labels)

        # Backward pass and optimize
        loss.backward()
        optimizer.step()

        running_loss += loss.item() * inputs.size(0)
        
        # Store predictions and labels for accuracy calculation
        preds = torch.sigmoid(outputs) > 0.5
        all_predictions.extend(preds.cpu().numpy())
        all_labels.extend(labels.cpu().numpy())

        if (i + 1) % 10 == 0: # Print progress every 10 batches
            print(f"  Batch {i+1}/{len(dataloader)}, Loss: {loss.item():.4f}")

    epoch_loss = running_loss / len(dataloader.dataset)
    epoch_acc = accuracy_score(np.array(all_labels).flatten(), np.array(all_predictions).flatten())
    return epoch_loss, epoch_acc

def validate_one_epoch(model, dataloader, criterion, device):
    """
    Validates the model on the validation set for one epoch.
    """
    model.eval()  # Set model to evaluation mode
    running_loss = 0.0
    all_labels = []
    all_predictions = []

    with torch.no_grad(): # No need to track gradients during validation
        for inputs, labels in dataloader:
            inputs = inputs.to(device)
            labels = labels.to(device).float().unsqueeze(1) # Ensure labels are float and have shape [batch_size, 1]

            outputs = model(inputs)
            loss = criterion(outputs, labels)
            running_loss += loss.item() * inputs.size(0)

            preds = torch.sigmoid(outputs) > 0.5
            all_predictions.extend(preds.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())

    epoch_loss = running_loss / len(dataloader.dataset)
    epoch_acc = accuracy_score(np.array(all_labels).flatten(), np.array(all_predictions).flatten())
    return epoch_loss, epoch_acc

def main():
    print(f"Using device: {DEVICE}")

    # --- 1. Initialize Model ---
    # The DeepfakeDetector class is imported from model.py
    # It uses EfficientNet-B0 with a custom classifier for binary classification.
    print("Initializing model...")
    model = DeepfakeDetector(num_classes=1) # num_classes=1 for binary (real/fake)
    model.to(DEVICE)
    print("Model initialized.")

    # --- 2. Define Loss Function and Optimizer ---
    # Binary Cross-Entropy with Logits Loss is suitable for binary classification
    # It combines a Sigmoid layer and the BCELoss in one single class for better numerical stability.
    criterion = nn.BCEWithLogitsLoss() 
    
    # Adam optimizer is a common choice for deep learning tasks.
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)
    
    print(f"Optimizer: Adam, Learning Rate: {LEARNING_RATE}")
    print(f"Loss Function: BCEWithLogitsLoss")

    # --- 3. Data Loaders (Placeholder) ---
    # TODO: Replace with actual DataLoaders from Phase 1 (preprocess.py and dataset loading)
    # The DataLoaders should load preprocessed frames (images) and their corresponding labels (real/fake).
    # Example of how data should be structured:
    #   - Input: Batches of image tensors, normalized as expected by EfficientNet.
    #            Shape: (BATCH_SIZE, 3, Height, Width) e.g., (32, 3, 224, 224) for EfficientNet-B0.
    #   - Labels: Batches of binary labels (0 for real, 1 for fake).
    #             Shape: (BATCH_SIZE) or (BATCH_SIZE, 1).
    #
    # You will need to:
    #   1. Create a custom PyTorch Dataset class (e.g., in a new `dataset.py` file or within `train.py`).
    #      This class will:
    #      - Read image paths and labels (e.g., from CSV files or directory structure created in Phase 1).
    #      - Load images using OpenCV or PIL.
    #      - Apply necessary transformations (resize, crop, normalization for EfficientNet, data augmentation).
    #        Normalization for ImageNet pre-trained models:
    #        mean = [0.485, 0.456, 0.406]
    #        std = [0.229, 0.224, 0.225]
    #   2. Instantiate DataLoader objects for training and validation sets using your custom Dataset.

    print("\n--- Placeholder Data ---")
    print("Using dummy data for demonstration. Replace with actual DataLoaders.")
    # Create dummy data for demonstration (100 samples, 3 channels, 224x224)
    # In a real scenario, frames would be loaded from files.
    num_samples_train = 128
    num_samples_val = 64
    dummy_train_images = torch.randn(num_samples_train, 3, 224, 224) 
    dummy_train_labels = torch.randint(0, 2, (num_samples_train,)) # 0 for real, 1 for fake
    dummy_val_images = torch.randn(num_samples_val, 3, 224, 224)
    dummy_val_labels = torch.randint(0, 2, (num_samples_val,))

    # Create TensorDatasets and DataLoaders
    train_dataset = TensorDataset(dummy_train_images, dummy_train_labels)
    val_dataset = TensorDataset(dummy_val_images, dummy_val_labels)
    
    train_loader = DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False)
    
    print(f"Number of training samples: {len(train_dataset)}")
    print(f"Number of validation samples: {len(val_dataset)}")
    print(f"Batch size: {BATCH_SIZE}")
    print("------------------------\n")

    # --- 4. Training Loop ---
    print(f"Starting training for {NUM_EPOCHS} epochs...\n")
    best_val_loss = float('inf')

    for epoch in range(NUM_EPOCHS):
        print(f"--- Epoch {epoch+1}/{NUM_EPOCHS} ---")

        train_loss, train_acc = train_one_epoch(model, train_loader, criterion, optimizer, DEVICE)
        print(f"Epoch {epoch+1} Training: Loss: {train_loss:.4f}, Accuracy: {train_acc:.4f}")

        val_loss, val_acc = validate_one_epoch(model, val_loader, criterion, DEVICE)
        print(f"Epoch {epoch+1} Validation: Loss: {val_loss:.4f}, Accuracy: {val_acc:.4f}")

        # Save the model if validation loss has decreased
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            model_save_path = "deepfake_detector/models/deepfake_detector_best.pth"
            # Ensure the 'models' directory exists (created in Phase 1 setup)
            # os.makedirs(os.path.dirname(model_save_path), exist_ok=True) # Should not be needed if setup was run
            torch.save(model.state_dict(), model_save_path)
            print(f"Model improved and saved to {model_save_path}")
        
        print("-" * 30)

    print("Training finished.")
    print(f"Best validation loss: {best_val_loss:.4f}")
    print(f"To use the trained model, load the weights from 'deepfake_detector/models/deepfake_detector_best.pth'")

    # --- 5. Video Data Handling Strategy ---
    # The current model processes individual frames.
    # To classify a whole video:
    # 1. Extract frames from the video using the `preprocess.py` script (from Phase 1).
    # 2. For each frame, apply the same transformations used during training and pass it through the trained model
    #    to get a prediction (probability of being fake).
    # 3. Aggregate predictions from all frames of a video. Common strategies:
    #    - Averaging: Calculate the average probability across all frames. If > 0.5, classify as fake.
    #    - Majority Vote: Classify each frame as real/fake. The class with more votes wins.
    #    - Temporal Modeling (Advanced): Use LSTMs, GRUs, or Transformers on sequences of frame features/predictions.
    #      This is deferred for now but would be a next step for improving performance.
    # The `DeepfakeDetector` model outputs logits. Apply `torch.sigmoid(output)` to get probabilities.

if __name__ == '__main__':
    # Note: This script expects that the 'deepfake_detector/models/' directory exists.
    # It was created as part of the Phase 1 setup. If not, uncomment the os.makedirs line in the training loop.
    main()
```
